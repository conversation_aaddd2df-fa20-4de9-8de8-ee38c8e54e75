function mysplit (inputstr, sep)
        if sep == nil then
                sep = "%s"
        end
        local t={}
        for str in string.gmatch(inputstr, "([^"..sep.."]+)") do
                table.insert(t, str)
        end
        return t
    end
    
    function CreateBlip(coords)
            blipRobbery = AddBlipForCoord(coords)
    
            SetBlipSprite(blipRobbery, 161)
            SetBlipScale(blipRobbery, 2.0)
            SetBlipColour(blipRobbery, 3)
    
            PulseBlip(blipRobbery)
    end
    
    function KillBlip()
            if blipRobbery then
                    RemoveBlip(blipRobbery)
            end
    end