local ESX = nil
if Config.oldESX then
    --TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    ESX = exports["es_extended"]:getSharedObject()
    RegisterNetEvent('esx:playerLoaded')
    AddEventHandler('esx:playerLoaded', function(xPlayer)
        ESX.PlayerData = xPlayer
        PlayerLoaded = true
    end)
end

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

Citizen.CreateThread(function()
	for k,v in pairs(Config.shops) do
	    local blip = AddBlipForCoord(v.coords)
		SetBlipSprite(blip, 313)
        SetBlipColour(blip, 1)
		SetBlipScale(blip, 0.8)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentSubstringPlayerName('非法｜抢劫')
		EndTextCommandSetBlipName(blip)
	end
end)

-- 新增函数来检查玩家是否是警察
function IsPoliceJob()
    for _, job in ipairs(Config.PoliceJobs) do
        if ESX.PlayerData.job.name == job then
            return true
        end
    end
    return false
end


Citizen.CreateThread(function()
    while not ESX.PlayerData do Citizen.Wait(100) end
    while not ESX.PlayerData.job do Citizen.Wait(100) end
    while true do
        Citizen.Wait(0)
        local wake = false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        if not holdingup then
            for k, v in pairs(Config.shops) do
                local dist = Vdist(coords, v.coords)
                if dist < Config.DrawDistance and not IsPoliceJob() then
                    DrawMarker(Config.marker.type, v.coords.x, v.coords.y, v.coords.z - 1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.marker.x, Config.marker.y, Config.marker.z, Config.marker.r, Config.marker.g, Config.marker.b, 100, false, false, 2, false, false, false, false)
                    wake = true
                    if dist < Config.marker.x then
                        ESX.ShowHelpNotification(_U('rob_help'))
                        if IsControlJustReleased(0, 38) then
                            if IsPedArmed(ped, 4) then
                                ESX.TriggerServerCallback('Rs_idrobbery:requestStart', function(cb)
                                    if cb then
                                        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'robber_id', {
                                            title = _U('robber_id')
                                            }, function(data, menu)
                                                local input = GetPlayerServerId(PlayerId())
                                                if data.value ~= nil then
                                                    input = GetPlayerServerId(PlayerId())..','..data.value
                                                end
                                                ESX.TriggerServerCallback('Rs_idrobbery:checkId', function(cb)
                                                    if cb then
                                                        menu.close()
                                                        holdingup = true
                                                        current = k
                                                        involved = true
                                                    end
                                                end, input, k)
                                        end, function(data, menu)
                                            menu.close()
                                        end)
                                    end
                                end, k)
                            else
                                ESX.ShowNotification(_U('not_armed'))
                            end
                        end
                    end
                    break
                end
            end
                else
            local dist = Vdist(coords, Config.shops[current].coords)
            if dist > Config.shops[current].maxDist then
                TriggerServerEvent('Rs_idrobbery:earlyExit')
                --ESX.ShowNotification(_U('early_exit'))
            end
        end
        if not wake then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('Rs_idrobbery:allStart')
AddEventHandler('Rs_idrobbery:allStart', function(shop, ids)
    CreateBlip(Config.shops[shop].coords)
    Citizen.CreateThread(function()
        awaitinginput = true
        while IsPoliceJob() and awaitinginput do
            Citizen.Wait(0)
            ESX.ShowHelpNotification(_U('cop_input_id'))
            if IsControlJustReleased(0, 38) then
                ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'cop_id', {
                    title = _U('cop_id')
                    }, function(data, menu)
                        if awaitinginput then
                            if data.value == nil then
                                ESX.ShowNotification(_U('invalid_input'))
                            else
                                TriggerServerEvent('Rs_idrobbery:copId', data.value)
                                menu.close()
                            end

                        else
                            ESX.ShowNotification(_U('input_done'))
                            menu.close()
                        end
                end, function(data, menu)
                    menu.close()
                end)
            end
        end
    end)
    local id = GetPlayerServerId(PlayerId())
    for i = 1, #ids do
        if ids[i] == id then
            involved = true
            break
        end
    end
    current = shop
    local reported = false
    while current == shop do
        Citizen.Wait(0)
        local wake = false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local dist = Vdist(coords, Config.shops[shop].coords)
        if dist < Config.DrawDistance2 then
            wake = true
            DrawMarker(Config.marker.type, Config.shops[shop].coords.x, Config.shops[shop].coords.y, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.shops[shop].maxDist * 2, Config.shops[shop].maxDist * 2, 255.0, Config.marker.r, Config.marker.g, Config.marker.b, 100, false, false, 2, false, false, false, false)
            if involved then
				if IsEntityDead(ped) and not reported then
					local kPed = GetPedSourceOfDeath(ped)
					local killer = 0
					local kPlayer = NetworkGetPlayerIndexFromPed(kPed)
					if kPlayer ~= PlayerId() then
						if (kPlayer ~= nil or kPlayer ~= -1) and kPed ~= 0 and IsEntityAPed(kPed) and IsEntityAPed(GetPedInVehicleSeat(kPed, -1)) and IsPedAPlayer(GetPedInVehicleSeat(kPed, -1)) then
							killer = NetworkGetPlayerIndexFromPed(GetPedInVehicleSeat(kPed, -1))
						else
							killer = NetworkGetPlayerIndexFromPed(kPed)
						end
						TriggerServerEvent("Rs_idrobbery:out",GetPlayerServerId(killer))
					end
					checkcopdie = false
				end
            elseif dist < Config.shops[shop].maxDist then
                local vehicle = GetVehiclePedIsIn(ped, false)
                if vehicle ~= 0 then
                    SetEntityCoords(vehicle, Config.safepos)
                else
                    --SetEntityCoords(ped, Config.safepos)
               end
            end
        end
        if not wake then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('Rs_idrobbery:copIdEnd')
AddEventHandler('Rs_idrobbery:copIdEnd', function(ids)
    awaitinginput = false
    local id = GetPlayerServerId(PlayerId())
    for i = 1, #ids do
        if ids[i] == tonumber(id) then
            involved = true
            break
        end
    end
end)

RegisterNetEvent('Rs_idrobbery:allEnd')
AddEventHandler('Rs_idrobbery:allEnd', function()
    KillBlip()
    current = nil
    involved = false
    holdingup = false
    awaitinginput = false
end)

-- Apple
local scores = {}
local RACING_HUD_COLOR = {238, 198, 78, 255}
local raceScoreColors = {
    {255, 140, 0, 255},
    {255, 20, 147, 255},
    {100, 149, 237, 255}
}

-- RegisterNetEvent('esx:playerLoaded')
-- AddEventHandler('esx:playerLoaded', function(xPlayer)
	-- ESX.TriggerServerCallback("Rs_idrobbery:getscores", function(getscores)
		-- scores = getscores
	-- end)
	
	-- CreateThread(function()
		-- while true do
			-- local sleep = 2000
			-- local player = PlayerPedId()
			-- if GetDistanceBetweenCoords( Config.showscores.x, Config.showscores.y, Config.showscores.z, GetEntityCoords(player)) < 15.0 then
				-- -- 抽签比赛名称
				-- sleep = 0
				-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z-0.600, '~h~~r~抢劫排行榜', RACING_HUD_COLOR, 4, 0.3, 0.3)
			-- end

			-- -- 当足够接近时，抽取分数
			-- if GetDistanceBetweenCoords(Config.showscores.x, Config.showscores.y, Config.showscores.z, GetEntityCoords(player)) < 15.0 then
			-- -- 如果我们收到了更新的分数，请显示它们
			-- local count = 0
			-- drawScores = {}
			-- for i = 1, #scores, 1 do
				-- table.insert(drawScores, scores[i].name..' | ~h~~w~【人头】: '..scores[i].mark)
			-- end

			-- -- 初始化偏移
			-- local zOffset = 0
			-- if (#drawScores > #raceScoreColors) then
				-- zOffset = 0.450*(#raceScoreColors) + 0.300*(#drawScores - #raceScoreColors - 1)
			-- else
				-- zOffset = 0.450*(#drawScores - 1)
			-- end

			-- -- 打印标题上方的分数
			-- for i = 1, #drawScores, 1 do
				-- -- 绘制带有颜色编码的分数文本
				-- if (i > #raceScoreColors) then
					-- -- 以白色绘制分数，递减偏移
					-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z+zOffset, drawScores[i], {255,255,255,255}, 4, 0.13, 0.13)
					-- zOffset = zOffset - 0.300
				-- else
					-- -- 用颜色和较大的文本绘制分数，减少偏移量
					-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z+zOffset, drawScores[i], raceScoreColors[i], 4, 0.22, 0.22)
					-- zOffset = zOffset - 0.450
				-- end
			-- end
			-- end
			-- Wait(sleep)
		-- end
	-- end)
-- end)

-- 显示三维文本的实用程序功能
function Draw3DText(x,y,z,textInput,colour,fontId,scaleX,scaleY)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px,py,pz, x,y,z, 1)
    local scale = (1/dist)*20
    local fov = (1/GetGameplayCamFov())*100
    local scale = scale*fov

    SetTextScale(scaleX*scale, scaleY*scale)
    SetTextFont(0)
    SetTextProportional(1)
    local colourr,colourg,colourb,coloura = table.unpack(colour)
    SetTextColour(colourr,colourg,colourb, coloura)
    SetTextDropshadow(2, 1, 1, 1, 255)
    SetTextEdge(3, 0, 0, 0, 150)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(textInput)
    SetDrawOrigin(x,y,z+2, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end

RegisterNetEvent('Rs_idrobbery:refersh')
AddEventHandler('Rs_idrobbery:refersh', function(newscores)
	scores = newscores
end)

-- MakeBlip = function(pos, addshop)
	-- local blip = AddBlipForCoord(pos.x,pos.y,pos.z)
	-- SetBlipSprite(blip,Config.BlipSprite)
	-- SetBlipDisplay(blip,2)
	-- SetBlipScale(blip,1.0)
	-- SetBlipColour(blip,Config.BlipColor)
	-- SetBlipDisplay(blip,4)
	-- SetBlipAsShortRange(blip,true)
	-- SetBlipHighDetail(blip,true)
	-- BeginTextCommandSetBlipName("STRING")
	-- AddTextComponentString('抢劫中')
	-- EndTextCommandSetBlipName(blip)
	-- local blip2 = AddBlipForRadius(pos.x,pos.y,pos.z, Config.radius) -- 安全區範圍
	-- SetBlipHighDetail(blip2, false)
	-- SetBlipColour(blip2, 77)
	-- SetBlipAlpha (blip2, 128) -- 加深顏色
	-- CreateThread(function()
		-- local cacheshop = addshop
		-- local pos = pos
		-- while blipRobbery[cacheshop] do
			-- local plyPos = GetEntityCoords(PlayerPedId())
			-- local dist = Vdist(plyPos,pos)
			-- local sleep = 1000
			-- if dist < Config.radius + 300.0 then
				-- sleep = 0
				-- DrawMarker(28, pos, 0, 0, 0, 0, 0, 0, 250.0, 250.0, 250.0, 255, 0, 0, 50, 0, 0, 2, 0, 0, 0, 0)
			-- end
			-- Wait(sleep)
		-- end
	-- end)

	-- return blip,blip2
-- end