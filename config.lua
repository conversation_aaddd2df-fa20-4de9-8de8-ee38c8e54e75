Config = {}

Config.oldESX = true

Config.DrawDistance = 10.0

Config.marker = {type = 1, x = 1.0, y = 1.0, z = 1.0, r = 250, g = 0, b = 0}

Config.Locale = 'zh'

-- 大标记指示 ROB 期间的 ROB 区域
Config.DrawDistance2 = 100.0

Config.marker2 = {type = 1, r = 250, g = 0, b = 0}

Config.safepos = vec3(216.4, -786.6, 30.8)


Config.PoliceJobs = {
    'police',       -- 市局
    'lssd1'  -- 分局
}


Config.shops = {

    ['5号超商'] = {
        coords = vec3(1160.861572, -313.674713, 69.197021),
        cops = 2,
        money1 = 200000,
        money2 = 200000,
        maxDist = 40.0,
        cd = 3600,
		Rank = 10,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },

    ['2号超商'] = {
        coords = vec3(-1220.02001953125, -915.9299926757812, 11.32999992370605),
        cops = 2,
        money1 = 200000,
        money2 = 200000,
        maxDist = 40.0,
        cd = 3600,
		Rank = 20,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },

    ['别墅突围'] = {
        coords = vec3(4.5008, 530.8779, 175.3428),
        cops = 2,
        money1 = 200000,
        money2 = 200000,
        maxDist = 40.0,
        cd = 3600,
		Rank = 20,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },

    ['别墅劫案'] = {
      coords = vec3(-804.4401, 177.3472, 76.7433),
      cops = 2,
      money1 = 200000,
      money2 = 200000,
      maxDist = 40.0,
      cd = 3600,
  Rank = 10,
  ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
  Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
  },

    ['珠宝店'] = {
      coords = vec3(-630.6235, -241.0526, 38.1622),
      cops = 2,
      money1 = 250000,
      money2 = 250000,
      maxDist = 40.0,
      cd = 3600,
  Rank = 20,
  ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
  Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
  },


  --  ['游乐园'] = {
   --     coords = vec3(-1667.9200439453125, -1073.6700439453125, 13.14999961853027),
   --     cops = 3,
   --     money1 = 60000,
   --     money2 = 70000,
   --     maxDist = 40.0,
   --     cd = 900,
	---	Rank = 20,
	---=-	ChanceToGetItem = 30, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
	---	Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
  --  },

    ['全福银行'] = {
        coords = vec3(379.5015, 332.2537, 103.5665),
        cops = 2,
        money1 = 200000,
        money2 = 200000,
        maxDist = 30.0,
        cd = 3600,
		Rank = 20,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },

    ['中部银行'] = {
        coords = vec3(-1211.2900390625, -335.2300109863281, 37.77999877929687),
        cops = 3,
        money1 = 250000,
        money2 = 250000,
        maxDist = 30.0,
        cd = 3600,
		Rank = 20,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },
	
	['北部银行'] = {
        coords = vec3(-103.58000183105467, 6477.72998046875, 31.6299991607666),
        cops = 3,
        money1 = 300000,
        money2 = 300000,
        maxDist = 30.0,
        cd = 3600,
		Rank = 20,
		ChanceToGetItem = 0, -- 如果数学随机（0-100）<=有机会获得物品，则给予物品
		Items = {'stone','stone','iron', 'iron', 'iron', 'copper', 'copper','copper', 'gold','diamond','emerald'}
    },
}

Config.showscores = vec3(-2192.81, -3728.37, 560.24)